'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { UserPlanUsage } from '@/lib/services/plan-service';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface UserProfileClientProps {
  user: any;
  planUsage: UserPlanUsage;
}

export function UserProfileClient({ user, planUsage }: UserProfileClientProps) {
  const [isUpgrading, setIsUpgrading] = useState(false);

  const handleUpgrade = () => {
    setIsUpgrading(true);
    // This would be replaced with actual payment flow
    alert('Payment flow would be implemented here');
    setIsUpgrading(false);
  };

  return (
    <div className="grid gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Account Information</CardTitle>
          <CardDescription>Your personal account details</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <Avatar className="h-16 w-16">
              <AvatarImage src={user.image || ''} alt={user.name} />
              <AvatarFallback>{user.name?.charAt(0) || 'U'}</AvatarFallback>
            </Avatar>
            <div>
              <h3 className="text-xl font-medium">{user.name}</h3>
              <p className="text-sm text-muted-foreground">{user.email}</p>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Member Since</span>
              <span>{new Date(user.createdAt).toLocaleDateString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Subscription Plan</CardTitle>
          <CardDescription>Your current plan and usage</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium">{planUsage.currentPlan} Plan</h3>
              {planUsage.currentPlan === 'FREE' && (
                <Button onClick={handleUpgrade} disabled={isUpgrading}>
                  {isUpgrading ? 'Processing...' : 'Upgrade Plan'}
                </Button>
              )}
            </div>
          </div>
          
          <div className="space-y-6">
            <div>
              <div className="flex justify-between mb-2">
                <span>Projects</span>
                <span>{planUsage.projectsUsed}/{planUsage.projectsLimit}</span>
              </div>
              <Progress value={(planUsage.projectsUsed / planUsage.projectsLimit) * 100} />
            </div>
            
            <div>
              <div className="flex justify-between mb-2">
                <span>Daily Optimizations</span>
                <span>{planUsage.dailyOptimizationsUsed}/{planUsage.dailyOptimizationsLimit}</span>
              </div>
              <Progress value={(planUsage.dailyOptimizationsUsed / planUsage.dailyOptimizationsLimit) * 100} />
            </div>
            
            <div>
              <div className="flex justify-between mb-2">
                <span>Monthly Optimizations</span>
                <span>{planUsage.monthlyOptimizationsUsed}/{planUsage.monthlyOptimizationsLimit}</span>
              </div>
              <Progress value={(planUsage