import { db } from "@/lib/database";
import { user, projects, optimizationUsage } from "@/lib/schema";
import { eq, and, count, gte, sql } from "drizzle-orm";
import { nanoid } from "nanoid";

export interface PlanLimits {
  maxProjects: number;
  dailyOptimizations: number;
  monthlyOptimizations: number;
}

export interface UserPlanUsage {
  currentPlan: string;
  projectsUsed: number;
  projectsLimit: number;
  dailyOptimizationsUsed: number;
  dailyOptimizationsLimit: number;
  monthlyOptimizationsUsed: number;
  monthlyOptimizationsLimit: number;
}

export class PlanService {
  private static getPlanLimits(planType: string): PlanLimits {
    switch (planType) {
      case 'FREE':
        return {
          maxProjects: parseInt(process.env.FREE_PLAN_MAX_PROJECTS || '2'),
          dailyOptimizations: parseInt(process.env.FREE_PLAN_DAILY_OPTIMIZATIONS || '5'),
          monthlyOptimizations: parseInt(process.env.FREE_PLAN_MONTHLY_OPTIMIZATIONS || '15'),
        };
      case 'PRO':
        return {
          maxProjects: 10,
          dailyOptimizations: 20,
          monthlyOptimizations: 100,
        };
      case 'ENTERPRISE':
        return {
          maxProjects: 100,
          dailyOptimizations: 100,
          monthlyOptimizations: 1000,
        };
      default:
        return {
          maxProjects: 2,
          dailyOptimizations: 5,
          monthlyOptimizations: 15,
        };
    }
  }

  static async getUserPlanUsage(userId: string): Promise<UserPlanUsage> {
    // Get user's plan
    const [userData] = await db
      .select({ plan: user.plan })
      .from(user)
      .where(eq(user.id, userId));

    const planType = userData?.plan || 'FREE';
    const limits = this.getPlanLimits(planType);

    // Get project count
    const [projectCount] = await db
      .select({ count: count() })
      .from(projects)
      .where(eq(projects.userId, userId));

    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];
    
    // Get current month in YYYY-MM format
    const currentMonth = today.substring(0, 7);

    // Get daily optimization count
    const [dailyCount] = await db
      .select({ count: count() })
      .from(optimizationUsage)
      .where(
        and(
          eq(optimizationUsage.userId, userId),
          eq(optimizationUsage.date, today)
        )
      );

    // Get monthly optimization count
    const [monthlyCount] = await db
      .select({ count: count() })
      .from(optimizationUsage)
      .where(
        and(
          eq(optimizationUsage.userId, userId),
          eq(optimizationUsage.month, currentMonth)
        )
      );

    return {
      currentPlan: planType,
      projectsUsed: projectCount?.count || 0,
      projectsLimit: limits.maxProjects,
      dailyOptimizationsUsed: dailyCount?.count || 0,
      dailyOptimizationsLimit: limits.dailyOptimizations,
      monthlyOptimizationsUsed: monthlyCount?.count || 0,
      monthlyOptimizationsLimit: limits.monthlyOptimizations,
    };
  }

  static async canCreateProject(userId: string): Promise<{ allowed: boolean; message?: string }> {
    const usage = await this.getUserPlanUsage(userId);
    
    if (usage.projectsUsed >= usage.projectsLimit) {
      return {
        allowed: false,
        message: `You've reached the maximum of ${usage.projectsLimit} projects on your ${usage.currentPlan} plan. Please upgrade to create more projects.`
      };
    }
    
    return { allowed: true };
  }

  static async canRunOptimization(userId: string): Promise<{ allowed: boolean; message?: string }> {
    const usage = await this.getUserPlanUsage(userId);
    
    if (usage.dailyOptimizationsUsed >= usage.dailyOptimizationsLimit) {
      return {
        allowed: false,
        message: `You've reached the daily limit of ${usage.dailyOptimizationsLimit} optimizations on your ${usage.currentPlan} plan. Please try again tomorrow or upgrade your plan.`
      };
    }
    
    if (usage.monthlyOptimizationsUsed >= usage.monthlyOptimizationsLimit) {
      return {
        allowed: false,
        message: `You've reached the monthly limit of ${usage.monthlyOptimizationsLimit} optimizations on your ${usage.currentPlan} plan. Your limit will reset next month, or you can upgrade your plan.`
      };
    }
    
    return { allowed: true };
  }

  static async trackOptimizationUsage(userId: string): Promise<void> {
    const today = new Date();
    const dateStr = today.toISOString().split('T')[0];
    const monthStr = dateStr.substring(0, 7);
    
    await db.insert(optimizationUsage).values({
      id: nanoid(),
      userId,
      timestamp: today,
      date: dateStr,
      month: monthStr,
    });
  }
}