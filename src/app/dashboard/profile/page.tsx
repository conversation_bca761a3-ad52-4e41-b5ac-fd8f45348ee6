import { auth } from '@/lib/auth';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import { PlanService } from '@/lib/services/plan-service';
import { UserProfileClient } from '@/components/profile/user-profile-client';

export default async function UserProfilePage() {
  // Get current session
  const session = await auth.api.getSession({
    headers: await headers()
  });

  if (!session) {
    redirect('/auth/login');
  }

  // Get user plan usage
  const planUsage = await PlanService.getUserPlanUsage(session.user.id);

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">User Profile</h1>
      
      <UserProfileClient 
        user={session.user} 
        planUsage={planUsage} 
      />
    </div>
  );
}